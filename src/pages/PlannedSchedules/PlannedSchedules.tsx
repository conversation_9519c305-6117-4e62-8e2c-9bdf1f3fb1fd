import { FC, useEffect, useState } from "react";
import { Container, ComboboxStyled, LoaderLabel, LoaderContainer, ButtonBack, HeaderActions, ButtonDownloadDc } from "./PlannedSchedules.style";
import { ActionBar, FilterDatePickerStyled } from "../Nsi/Nsi.style";
import { useLocation, useNavigate } from "react-router-dom";
import queryString from "query-string";
import { DistributionContainer } from "./components/DistributionContainer";
import { ViewContainer } from "./components/ViewContainer";
import { isModeCenter } from "utils/getMode";
import { useStores } from "../../stores/useStore";
import { observer } from "mobx-react";
import { AccessControl } from "components/AccessControl";
import { Loader } from "components/Loader";
import { prepareDateTable } from "../../helpers/DateUtils";

export const PlannedSchedules: FC = observer(() => {
  const location = useLocation();
  const history = useNavigate();
  const { authStore, liveTimerStore, plannedSchedulesStore } = useStores();
  const { isCenter } = authStore;
  const { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR } = liveTimerStore;
  const { day = DEFAULT_DAY, month = DEFAULT_MONTH, year = DEFAULT_YEAR, viewPage = "view", selectedPG }: any = queryString.parse(location.search);

  const { plannedSchedules, isLoadingFile } = plannedSchedulesStore;

  const comboboxList =
    plannedSchedules?.length > 0
      ? plannedSchedules.map((item: any) => ({
          value: item.tabId,
          label: `${item.name}  | ${prepareDateTable(item.createdAt)} | ${item.type} | ${item.syncZone} | ${item.source} ${item?.globalAccept ? "|" : ""}  ${
            item?.globalAccept ? (item?.globalAccept?.type === "MANUAL" ? "Ручной" : "Автоматический") : ""
          } ${item?.globalAccept?.author ?? ""}
    `,
        }))
      : [];

  useEffect(() => {
    const finYear = year ?? DEFAULT_YEAR;
    const finMonth = month ?? DEFAULT_MONTH;
    const finDay = day ?? DEFAULT_DAY;
    if (finYear && finMonth && finDay) {
      if (!isCenter) history(`?year=${year}&month=${month}&day=${day}&viewPage=view`);
    }
  }, [isCenter, DEFAULT_DAY]);

  useEffect(() => {
    if (isModeCenter && year && month && day) {
      plannedSchedulesStore.initPlannedSchedules(year, month, day);
    }
  }, [year, month, day]);

  useEffect(() => {
    return () => {
      localStorage.removeItem("table-checked-items-7");
      localStorage.removeItem(`tableOpened-${7}`);
      plannedSchedulesStore.stopDistribution();
      plannedSchedulesStore.stopView();
      plannedSchedulesStore.stopStatusPlanned();
    };
  }, [day, month, year]);

  const [isModalLoad, setIsModalLoad] = useState(false);

  const [selected, setSelected] = useState<any[]>([]);

  const [selectedDown, setSelectedDown] = useState([]);

  useEffect(() => {
    return () => {
      localStorage.removeItem("calendar-date");
      localStorage.removeItem("selectedPG");
      //
      plannedSchedulesStore.stopDistribution();
      plannedSchedulesStore.stopView();
      plannedSchedulesStore.stopStatusPlanned();
      plannedSchedulesStore.stopAckPolling();
      //
      localStorage.removeItem(`table-sort-52_v2`);
      localStorage.removeItem(`table-sort-51_v2`);
      localStorage.removeItem(`table-sort-53`);
      localStorage.removeItem(`table-sort-7`);
    };
  }, []);

  useEffect(() => {
    if (isModeCenter && isCenter) {
      plannedSchedulesStore.startAckPolling();
    } else {
      plannedSchedulesStore.stopAckPolling();
      plannedSchedulesStore.clearAckMessages();
    }

    return () => {
      plannedSchedulesStore.stopAckPolling();
      plannedSchedulesStore.clearAckMessages();
    };
  }, [isCenter]);

  useEffect(() => {
    const lastTaskId = plannedSchedules.find(({ tabId }: { tabId: string }) => tabId === selectedPG)?.lastTaskId ?? null;
    plannedSchedulesStore.changeLastTaskId(lastTaskId);
  }, [selectedPG]);

  return (
    <Container>
      <ActionBar>
        <FilterDatePickerStyled
          day={day}
          month={month}
          year={year}
          onClick={async ({ day, month, year }) => {
            if (viewPage === "distribution") {
              await plannedSchedulesStore.initPlannedSchedules(year, month, day).then((finArr: any) => {
                const [first] = finArr;
                setSelected(first?.pgId ? [first?.pgId] : []);
                history(`?year=${year}&month=${month}&day=${day}&viewPage=${viewPage}&selectedPG=${first?.pgId ?? null}`);
                localStorage.setItem("calendar-date", JSON.stringify({ day, month, year }));
              });
            } else {
              setSelected([]);
              history(`?year=${year}&month=${month}&day=${day}&viewPage=${viewPage}&selectedPG=${null}`);
            }
          }}
          dataTest="planned-schedules-view-pg.date-picker"
        />
        {viewPage === "distribution" && (
          <ComboboxStyled
            items={comboboxList}
            selectedValue={selectedPG ?? null}
            width={800}
            placeholder="Список загруженных ПГ"
            onChange={async ({ value }) => {
              plannedSchedulesStore.stopDistribution();
              plannedSchedulesStore.stopView();
              history(`?year=${year}&month=${month}&day=${day}&viewPage=${viewPage}&selectedPG=${value ?? null}`);
              setSelectedDown([]);
            }}
            dataTest="opened-pg-header.selected-pg-combobox"
          />
        )}
        {isModeCenter && isCenter ? (
          <HeaderActions>
            {viewPage !== "view" && (
              <ButtonBack
                icon="back"
                title="Возврат к списку ПГ"
                onClick={() => {
                  history(`?year=${year}&month=${month}&day=${day}&viewPage=view`);
                  localStorage.removeItem(`tableOpened-${5}`);
                  localStorage.removeItem(`tableOpened-${6}`);
                }}
                dataTest="opened-pg-header.back-button"
              />
            )}
          </HeaderActions>
        ) : (
          <AccessControl rules={["engineer", "nsi_admin"]}>
            {isLoadingFile ? (
              <LoaderContainer>
                <Loader spinnerSize={14} />
                <LoaderLabel>Идет загрузка</LoaderLabel>
              </LoaderContainer>
            ) : (
              <ButtonDownloadDc title="Резервная загрузка ПГ" type="secondary" onClick={() => setIsModalLoad(true)} dataTest="planned-schedules-in-dc.upload-pg-btn" />
            )}
          </AccessControl>
        )}
      </ActionBar>
      {viewPage === "view" ? (
        <ViewContainer
          isModalLoad={isModalLoad}
          setIsModalLoad={setIsModalLoad}
          year={String(year)}
          month={String(month)}
          day={String(day)}
          isCenter={isCenter}
          selected={selected}
          setSelected={setSelected}
        />
      ) : (
        <DistributionContainer
          selectedPG={selectedPG === "null" ? null : selectedPG}
          year={year}
          month={month}
          day={day}
          selectedDown={selectedDown}
          setSelectedDown={setSelectedDown}
        />
      )}
    </Container>
  );
});
