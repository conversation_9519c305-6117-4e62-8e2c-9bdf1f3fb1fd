import { NsiStore } from "./NsiStore";
import { NotificationStore } from "./NotificationStore/NotificationStore";
import { PlannedSchedulesStore } from "./PlannedSchedulesStore/PlannedSchedulesStore";
import { SettingsStore } from "./SettingsStore";
import { LiveTimerStore } from "./LiveTimerStore";
import { UserActionsStore } from "./UserActionsStore";
import { AdministrationStore } from "./AdministrationStore";
import { NotificationsStore } from "./NotificationsStore/NotificationsStore";
import { AuthStore } from "./AuthStore";
import { TableStore } from "./TableStore/TableStore";
import { AckModalStore } from "./AckModalStore";

export class RootStore {
  nsiStore: NsiStore;
  notificationStore: NotificationStore;
  notificationsStore: NotificationsStore;
  plannedSchedulesStore: PlannedSchedulesStore;
  settingsStore: SettingsStore;
  liveTimerStore: LiveTimerStore;
  userActionsStore: UserActionsStore;
  administrationStore: AdministrationStore;
  authStore: AuthStore;
  tableStore: TableStore;
  ackModalStore: AckModalStore;

  constructor() {
    this.nsiStore = new NsiStore(this);
    this.notificationStore = new NotificationStore(this);
    this.notificationsStore = new NotificationsStore(this);
    this.plannedSchedulesStore = new PlannedSchedulesStore(this);
    this.settingsStore = new SettingsStore(this);
    this.userActionsStore = new UserActionsStore(this);
    this.liveTimerStore = new LiveTimerStore(this);
    this.administrationStore = new AdministrationStore(this);
    this.authStore = new AuthStore(this);
    this.tableStore = new TableStore(this);
    this.ackModalStore = new AckModalStore(this);
  }
}

declare global {
  interface Window {
    _____APP_STATE_____: RootStore;
  }
}

const stores = new RootStore();
window._____APP_STATE_____ = stores;
export { stores };
