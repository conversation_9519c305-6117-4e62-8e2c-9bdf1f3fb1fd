import { observer } from "mobx-react";
import { FC } from "react";
import styled from "styled-components";
import { Modal } from "~/components/Modal";
import { useStores } from "~/stores/useStore";

const ScrollableContent = styled.div`
  height: 100%;
  overflow-y: auto;
  padding: 20px 30px 20px 20px; // Небольшой отступ, чтобы скроллбар не прилипал к тексту
  white-space: pre-wrap; // Сохраняем переносы строк из текста ошибки
  word-break: break-word;
  position: relative;

  // Горизонтальная линия-разделитель
  &::after {
    content: "";
    display: block;
    height: 1px;
    background-color: #e5e5e5;
    margin: 10px -20px 0 -20px;
  }
`;

/**
 * Глобальный компонент для отображения модальных окон квитирования
 * Отображается поверх всех разделов приложения и не исчезает при навигации
 */
export const GlobalAckModal: FC = observer(() => {
  const { ackModalStore } = useStores();
  const { isModalVisible, currentMessage } = ackModalStore;

  // Если модальное окно не должно отображаться или нет текущего сообщения, не рендерим ничего
  if (!isModalVisible || !currentMessage) {
    return null;
  }

  const handleConfirm = async () => {
    await ackModalStore.acknowledgeCurrentMessage();
  };

  const title = `Квитирование ошибки по ${currentMessage.pgName}`;

  return (
    <Modal 
      title={title} 
      confirmText="ОК" 
      onConfirm={handleConfirm} 
      width={600} 
      height={300} 
      hideCloseIcon 
      scrollableContent
      // Устанавливаем высокий z-index, чтобы модальное окно отображалось поверх всех элементов
      style={{ zIndex: 10000 }}
    >
      <ScrollableContent>{currentMessage.text}</ScrollableContent>
    </Modal>
  );
});
