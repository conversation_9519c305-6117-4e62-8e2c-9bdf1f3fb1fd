import React from "react";
import styled from "styled-components";
import { observer } from "mobx-react";
import { useStores } from "~/stores/useStore";
import { addMockAckMessage, addAllMockAckMessages } from "~/utils/mockAckMessages";

const TestButtonsContainer = styled.div`
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #ddd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const TestButton = styled.button`
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
  
  &:hover {
    background: #0056b3;
  }
  
  &:active {
    background: #004085;
  }
`;

const ClearButton = styled(TestButton)`
  background: #dc3545;
  
  &:hover {
    background: #c82333;
  }
  
  &:active {
    background: #a71e2a;
  }
`;

const InfoText = styled.div`
  font-size: 10px;
  color: #666;
  text-align: center;
  margin-bottom: 4px;
`;

/**
 * Компонент для тестирования модального окна квитирования
 * Отображает кнопки для добавления моковых сообщений
 * Показывается только в режиме разработки
 */
export const AckModalTestButtons: React.FC = observer(() => {
  const { ackModalStore } = useStores();
  
  // Показываем кнопки только в режиме разработки
  if (process.env.NODE_ENV === "production") {
    return null;
  }

  const handleAddSingleMessage = () => {
    addMockAckMessage(ackModalStore, 0);
  };

  const handleAddWarningMessage = () => {
    addMockAckMessage(ackModalStore, 1);
  };

  const handleAddInfoMessage = () => {
    addMockAckMessage(ackModalStore, 2);
  };

  const handleAddAllMessages = () => {
    addAllMockAckMessages(ackModalStore);
  };

  const handleAddRandomMessage = () => {
    ackModalStore.addRandomMockMessage();
  };

  const handleClearMessages = () => {
    ackModalStore.clearAllMessages();
  };

  const messageCount = ackModalStore.ackMessages.length;
  const isModalVisible = ackModalStore.isModalVisible;

  return (
    <TestButtonsContainer>
      <InfoText>
        Тест модального окна квитирования
        <br />
        Сообщений: {messageCount} | Модальное окно: {isModalVisible ? "Показано" : "Скрыто"}
      </InfoText>
      
      <TestButton onClick={handleAddSingleMessage}>
        Добавить ошибку
      </TestButton>
      
      <TestButton onClick={handleAddWarningMessage}>
        Добавить предупреждение
      </TestButton>
      
      <TestButton onClick={handleAddInfoMessage}>
        Добавить информацию
      </TestButton>
      
      <TestButton onClick={handleAddRandomMessage}>
        Случайное сообщение
      </TestButton>
      
      <TestButton onClick={handleAddAllMessages}>
        Добавить все (3 шт.)
      </TestButton>
      
      <ClearButton onClick={handleClearMessages}>
        Очистить все
      </ClearButton>
    </TestButtonsContainer>
  );
});
