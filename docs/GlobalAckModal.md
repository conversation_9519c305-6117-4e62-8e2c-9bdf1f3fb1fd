# Глобальное модальное окно квитирования

## Описание

Реализована система глобального модального окна квитирования, которое отображается поверх всех разделов системы в Централизованной части и не исчезает при смене страниц до тех пор, пока пользователь не нажмет "ОК".

## Архитектура решения

### 1. AckModalStore (`src/stores/AckModalStore/`)

Глобальный стор для управления состоянием модальных окон квитирования:

- **ackMessages**: массив сообщений для квитирования
- **isModalVisible**: флаг видимости модального окна
- **currentMessage**: текущее отображаемое сообщение

**Основные методы:**
- `addAckMessage(message)` - добавить сообщение
- `addAckMessages(messages)` - добавить несколько сообщений
- `removeAckMessage(messageId)` - удалить сообщение
- `acknowledgeCurrentMessage()` - квитировать текущее сообщение
- `clearAllMessages()` - очистить все сообщения

### 2. GlobalAckModal (`src/components/GlobalAckModal/`)

Глобальный компонент модального окна, который:
- Отображается на уровне App.tsx
- Имеет высокий z-index (10000) для отображения поверх всех элементов
- Автоматически показывает первое сообщение из очереди
- Использует тот же дизайн, что и оригинальный AckErrorModal

### 3. Интеграция с PlannedSchedulesStore

PlannedSchedulesStore обновлен для работы с глобальным стором:
- `fetchAckMessages()` - теперь добавляет сообщения в глобальный стор
- `acknowledgeMessage()` - удаляет сообщения из глобального стора
- Сохранена обратная совместимость с локальным массивом `ackMessages`

## Тестирование

### Моковые данные

Созданы моковые данные в `src/utils/mockAckMessages.ts`:
- 3 предустановленных сообщения разных типов (ERROR, WARNING, INFO)
- Функция создания случайных сообщений
- Утилиты для добавления сообщений в стор

### Тестовые кнопки

Компонент `AckModalTestButtons` (отображается только в режиме разработки):
- Кнопки для добавления разных типов сообщений
- Кнопка для очистки всех сообщений
- Отображение текущего состояния (количество сообщений, видимость модального окна)

## Использование

### Добавление сообщения программно

```typescript
import { useStores } from "~/stores/useStore";

const { ackModalStore } = useStores();

// Добавить одно сообщение
ackModalStore.addAckMessage({
  id: "unique-id",
  pgId: "pg-001", 
  pgName: "Название ПГ",
  text: "Текст сообщения",
  kind: "ERROR",
  createdAt: new Date().toISOString()
});

// Добавить моковое сообщение для тестирования
ackModalStore.addMockMessage();
```

### Тестирование в браузере

1. Запустите приложение: `npm run start:center`
2. В правом верхнем углу появятся тестовые кнопки (только в dev режиме)
3. Нажмите любую кнопку для добавления сообщения
4. Модальное окно появится поверх всех элементов
5. Попробуйте сменить страницу - модальное окно останется видимым
6. Нажмите "ОК" для квитирования сообщения

## Особенности реализации

1. **Глобальность**: Модальное окно отображается на уровне App.tsx, поэтому видно на всех страницах
2. **Очередь сообщений**: Если есть несколько сообщений, они показываются по одному
3. **Высокий z-index**: Модальное окно отображается поверх всех элементов интерфейса
4. **Обратная совместимость**: Старый код продолжает работать благодаря сохранению локального состояния
5. **Автоматическое управление**: При добавлении сообщений модальное окно автоматически показывается

## Файлы, которые были изменены

- `src/stores/AckModalStore/` - новый глобальный стор
- `src/components/GlobalAckModal/` - новый глобальный компонент
- `src/components/AckModalTestButtons/` - компонент для тестирования
- `src/utils/mockAckMessages.ts` - моковые данные
- `src/stores/RootStore.ts` - интеграция нового стора
- `src/App.tsx` - добавление глобального компонента
- `src/stores/PlannedSchedulesStore/PlannedSchedulesStore.ts` - интеграция с глобальным стором
- `src/pages/PlannedSchedules/PlannedSchedules.tsx` - удаление локального отображения

## Требования выполнены

✅ Модальное окно отображается поверх всех разделов системы
✅ Окно не исчезает при смене страниц
✅ Пользователь должен нажать "ОК" для закрытия
✅ Добавлена моковая реализация для тестирования
✅ Сохранена функциональность оригинального компонента
