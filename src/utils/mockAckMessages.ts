import { AckMessage } from "~/api/plannedSchedules/plannedSchedules-controller";

/**
 * Моковые данные для тестирования модального окна квитирования
 */
export const mockAckMessages: AckMessage[] = [
  {
    id: "mock-ack-1",
    pgId: "pg-001",
    pgName: "ПГ Тестовая №1",
    text: "Обнаружена критическая ошибка в работе системы.\n\nДетали ошибки:\n- Превышено время ожидания ответа от внешней системы\n- Код ошибки: ERR_TIMEOUT_001\n- Время возникновения: 15:30:45\n\nТребуется немедленное вмешательство оператора.",
    kind: "ERROR",
    createdAt: new Date().toISOString(),
  },
  {
    id: "mock-ack-2", 
    pgId: "pg-002",
    pgName: "ПГ Производственная №5",
    text: "Предупреждение о возможной нестабильности соединения.\n\nИнформация:\n- Качество сигнала снижено до 75%\n- Рекомендуется проверить состояние каналов связи\n- Автоматическое восстановление через 10 минут",
    kind: "WARNING",
    createdAt: new Date(Date.now() - 60000).toISOString(), // 1 минута назад
  },
  {
    id: "mock-ack-3",
    pgId: "pg-003", 
    pgName: "ПГ Резервная №2",
    text: "Информационное сообщение о плановом обслуживании.\n\nПлановые работы:\n- Обновление конфигурации\n- Время выполнения: 2-3 минуты\n- Влияние на работу системы минимальное\n\nДанное сообщение носит информационный характер.",
    kind: "INFO",
    createdAt: new Date(Date.now() - 120000).toISOString(), // 2 минуты назад
  }
];

/**
 * Функция для добавления мокового сообщения в глобальный стор
 * Используется для тестирования без необходимости получения данных с бэкенда
 */
export const addMockAckMessage = (ackModalStore: any, messageIndex: number = 0) => {
  if (messageIndex >= 0 && messageIndex < mockAckMessages.length) {
    ackModalStore.addAckMessage(mockAckMessages[messageIndex]);
  } else {
    // Если индекс не указан или неверный, добавляем первое сообщение
    ackModalStore.addAckMessage(mockAckMessages[0]);
  }
};

/**
 * Функция для добавления всех моковых сообщений
 */
export const addAllMockAckMessages = (ackModalStore: any) => {
  ackModalStore.addAckMessages(mockAckMessages);
};

/**
 * Функция для создания случайного мокового сообщения
 */
export const createRandomMockMessage = (): AckMessage => {
  const kinds = ["ERROR", "WARNING", "INFO"];
  const pgNames = ["ПГ Тестовая", "ПГ Производственная", "ПГ Резервная", "ПГ Основная"];
  const randomKind = kinds[Math.floor(Math.random() * kinds.length)];
  const randomPgName = pgNames[Math.floor(Math.random() * pgNames.length)];
  const randomId = `mock-random-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  
  const errorTexts = [
    "Критическая ошибка в работе системы. Требуется немедленное вмешательство.",
    "Ошибка соединения с внешней системой. Проверьте сетевые настройки.",
    "Превышено время ожидания ответа. Операция прервана.",
  ];
  
  const warningTexts = [
    "Предупреждение о возможной нестабильности соединения.",
    "Качество сигнала снижено. Рекомендуется проверить оборудование.",
    "Обнаружены незначительные отклонения в работе системы.",
  ];
  
  const infoTexts = [
    "Информационное сообщение о плановом обслуживании.",
    "Система работает в штатном режиме.",
    "Выполнено обновление конфигурации.",
  ];
  
  let text = "";
  switch (randomKind) {
    case "ERROR":
      text = errorTexts[Math.floor(Math.random() * errorTexts.length)];
      break;
    case "WARNING":
      text = warningTexts[Math.floor(Math.random() * warningTexts.length)];
      break;
    case "INFO":
      text = infoTexts[Math.floor(Math.random() * infoTexts.length)];
      break;
  }
  
  return {
    id: randomId,
    pgId: `pg-${Math.floor(Math.random() * 999) + 1}`,
    pgName: `${randomPgName} №${Math.floor(Math.random() * 10) + 1}`,
    text: text + `\n\nВремя создания: ${new Date().toLocaleString()}\nID сообщения: ${randomId}`,
    kind: randomKind,
    createdAt: new Date().toISOString(),
  };
};
