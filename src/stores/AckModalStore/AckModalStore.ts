import { makeObservable, observable, action, runInAction } from "mobx";
import { AckMessage } from "~/api/plannedSchedules/plannedSchedules-controller";
import { RootStore } from "../RootStore";

export class AckModalStore {
  rootStore: RootStore;

  // Массив сообщений для квитирования
  ackMessages: AckMessage[] = [];

  // Флаг для отображения модального окна
  isModalVisible: boolean = false;

  // Текущее отображаемое сообщение
  currentMessage: AckMessage | null = null;

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;

    makeObservable(this, {
      ackMessages: observable,
      isModalVisible: observable,
      currentMessage: observable,
      setAckMessages: action,
      addAckMessage: action,
      removeAckMessage: action,
      showModal: action,
      hideModal: action,
      setCurrentMessage: action,
      clearAllMessages: action,
    });
  }

  /**
   * Установить массив сообщений для квитирования
   */
  setAckMessages(messages: AckMessage[]) {
    this.ackMessages = messages;
    this.updateModalVisibility();
  }

  /**
   * Добавить новое сообщение для квитирования
   */
  addAckMessage(message: AckMessage) {
    // Проверяем, что сообщение с таким id еще не существует
    const existingMessage = this.ackMessages.find((msg) => msg.id === message.id);
    if (!existingMessage) {
      this.ackMessages.push(message);
      this.updateModalVisibility();
    }
  }

  /**
   * Добавить несколько новых сообщений для квитирования
   */
  addAckMessages(messages: AckMessage[]) {
    const existingIds = new Set(this.ackMessages.map((m) => m.id));
    const newMessages = messages.filter((m) => !existingIds.has(m.id));

    if (newMessages.length > 0) {
      this.ackMessages.push(...newMessages);
      this.updateModalVisibility();
    }
  }

  /**
   * Удалить сообщение по ID
   */
  removeAckMessage(messageId: string) {
    this.ackMessages = this.ackMessages.filter((msg) => msg.id !== messageId);

    // Если удаляемое сообщение было текущим, скрываем модальное окно
    if (this.currentMessage?.id === messageId) {
      this.currentMessage = null;
      this.isModalVisible = false;
    }

    this.updateModalVisibility();
  }

  /**
   * Показать модальное окно с указанным сообщением
   */
  showModal(message: AckMessage) {
    this.currentMessage = message;
    this.isModalVisible = true;
  }

  /**
   * Скрыть модальное окно
   */
  hideModal() {
    this.isModalVisible = false;
    this.currentMessage = null;
  }

  /**
   * Установить текущее сообщение
   */
  setCurrentMessage(message: AckMessage | null) {
    this.currentMessage = message;
  }

  /**
   * Очистить все сообщения
   */
  clearAllMessages() {
    this.ackMessages = [];
    this.currentMessage = null;
    this.isModalVisible = false;
  }

  /**
   * Обновить видимость модального окна на основе наличия сообщений
   */
  private updateModalVisibility() {
    if (this.ackMessages.length > 0 && !this.isModalVisible) {
      // Показываем первое сообщение из очереди
      this.showModal(this.ackMessages[0]);
    } else if (this.ackMessages.length === 0) {
      // Если сообщений нет, скрываем модальное окно
      this.hideModal();
    }
  }

  /**
   * Обработать подтверждение квитирования
   */
  async acknowledgeCurrentMessage() {
    if (!this.currentMessage) return;

    const messageId = this.currentMessage.id;

    try {
      // Вызываем API для удаления сообщения на бэкенде
      await this.rootStore.plannedSchedulesStore.acknowledgeMessage(messageId);

      // Удаляем сообщение из локального состояния
      runInAction(() => {
        this.removeAckMessage(messageId);
      });
    } catch (error) {
      console.error("Ошибка при квитировании сообщения:", error);
      // В случае ошибки можно показать уведомление пользователю
      this.rootStore.notificationStore.addNotification({
        title: "Ошибка",
        description: "Не удалось квитировать сообщение. Попробуйте еще раз.",
        icon: "error",
        type: "error",
        isTimer: true,
      });
    }
  }

  /**
   * Добавить моковое сообщение для тестирования
   */
  addMockMessage() {
    const mockMessage: AckMessage = {
      id: `mock-${Date.now()}`,
      pgId: "test-pg-001",
      pgName: "Тестовая ПГ",
      text: 'Это тестовое сообщение для квитирования.\n\nОно содержит несколько строк текста для демонстрации работы модального окна.\n\nПосле нажатия "ОК" это сообщение должно исчезнуть.',
      kind: "ERROR",
      createdAt: new Date().toISOString(),
    };

    this.addAckMessage(mockMessage);
  }

  /**
   * Добавить случайное моковое сообщение для тестирования
   */
  addRandomMockMessage() {
    // Импортируем функцию создания случайного сообщения
    import("~/utils/mockAckMessages").then(({ createRandomMockMessage }) => {
      const mockMessage = createRandomMockMessage();
      this.addAckMessage(mockMessage);
    });
  }
}
